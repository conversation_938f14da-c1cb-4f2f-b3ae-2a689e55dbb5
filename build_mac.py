import os
import sys
import shutil
import subprocess
from pathlib import Path

def run_command(command):
    """运行命令并打印输出"""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        shell=True
    )
    
    for line in process.stdout:
        print(line, end='')
    
    process.wait()
    return process.returncode

def build_mac_app():
    """构建 Mac 应用程序"""
    print("开始构建 Mac 应用程序...")
    
    # 清理旧的构建文件
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    # 使用 PyInstaller 打包
    pyinstaller_cmd = (
        "pyinstaller "
        "--name='图片合并工具' "
        "--windowed "  # 不显示控制台窗口
        "--icon=app_icon.icns "  # 应用图标
        "--add-data='config.json:.' "  # 包含配置文件
        "--clean "  # 清理临时文件
        "--noconfirm "  # 不确认覆盖
        "main.py"
    )
    
    if run_command(pyinstaller_cmd) != 0:
        print("打包失败！")
        return False
    
    print("应用程序打包完成！")
    return True

def create_dmg():
    """创建 DMG 安装包"""
    print("开始创建 DMG 安装包...")
    
    # 创建临时目录
    temp_dir = "temp_dmg"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 复制应用程序到临时目录
    app_path = os.path.join("dist", "图片合并工具.app")
    shutil.copytree(app_path, os.path.join(temp_dir, "图片合并工具.app"))
    
    # 创建 DMG 文件
    dmg_path = "图片合并工具.dmg"
    if os.path.exists(dmg_path):
        os.remove(dmg_path)
    
    create_dmg_cmd = (
        f"hdiutil create -volname '图片合并工具' "
        f"-srcfolder {temp_dir} "
        f"-ov -format UDZO {dmg_path}"
    )
    
    if run_command(create_dmg_cmd) != 0:
        print("创建 DMG 失败！")
        return False
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print("DMG 安装包创建完成！")
    return True

def main():
    # 检查是否安装了必要的工具
    try:
        import PyInstaller
    except ImportError:
        print("正在安装 PyInstaller...")
        run_command("pip install pyinstaller")
    
    # 构建应用程序
    if not build_mac_app():
        return
    
    # 创建 DMG 安装包
    if not create_dmg():
        return
    
    print("\n打包完成！")
    print("安装包位置：图片合并工具.dmg")

if __name__ == "__main__":
    main() 