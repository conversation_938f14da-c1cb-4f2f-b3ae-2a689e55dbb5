import os
import shutil
import subprocess

def run_command(cmd):
    """运行命令并返回退出码"""
    try:
        subprocess.run(cmd, shell=True, check=True)
        return 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        return e.returncode

def build_windows_exe():
    """构建 Windows 可执行文件"""
    print("开始构建 Windows 应用程序...")
    
    # 清理旧的构建文件
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    # 使用 PyInstaller 打包
    pyinstaller_cmd = (
        "pyinstaller "
        "--name=\"图片合并工具\" "
        "--windowed "
        "--icon=app_icon.ico "
        "--add-data=\"config.json;.\" "
        "--clean "
        "--noconfirm "
        "main.py"
    )
    
    if run_command(pyinstaller_cmd) != 0:
        print("打包失败！")
        return False
    
    print("应用程序打包完成！")
    return True

if __name__ == "__main__":
    build_windows_exe()