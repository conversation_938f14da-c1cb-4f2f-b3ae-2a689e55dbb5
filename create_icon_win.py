from PIL import Image
import os

def create_windows_icon():
    """创建Windows图标文件"""
    print("开始创建Windows图标文件...")
    
    # 检查源图片是否存在
    if not os.path.exists('app_icon.png'):
        print("错误：未找到源图片 app_icon.png")
        return False
    
    try:
        # 打开源图片
        img = Image.open('app_icon.png')
        
        # 创建不同尺寸的图标
        sizes = [16, 32, 48, 64, 128, 256]
        icons = []
        
        for size in sizes:
            # 调整图片大小
            resized_img = img.resize((size, size), Image.Resampling.LANCZOS)
            icons.append(resized_img)
        
        # 保存为ICO文件
        icons[0].save(
            'app_icon.ico',
            format='ICO',
            sizes=[(size, size) for size in sizes],
            append_images=icons[1:]
        )
        
        print("Windows图标文件创建成功！")
        return True
        
    except Exception as e:
        print(f"创建图标时出错：{e}")
        return False

if __name__ == '__main__':
    create_windows_icon()