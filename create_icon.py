from PIL import Image, ImageDraw, ImageFont
import os
import shutil

def create_app_icon():
    """创建应用图标"""
    # 创建不同尺寸的图标
    sizes = [16, 32, 64, 128, 256, 512, 1024]
    icons = []
    
    for size in sizes:
        # 创建新图像
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制圆形背景
        margin = size // 8
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(52, 152, 219, 255))
        
        # 绘制图片合并图标
        icon_margin = size // 4
        # 绘制三个重叠的矩形
        rect_width = size - 2 * icon_margin
        rect_height = (size - 2 * icon_margin) // 3
        
        colors = [(231, 76, 60, 255),  # 红色
                 (46, 204, 113, 255),  # 绿色
                 (52, 152, 219, 255)]  # 蓝色
        
        for i, color in enumerate(colors):
            y = icon_margin + i * (rect_height - rect_height // 3)
            draw.rectangle([icon_margin, y, 
                          icon_margin + rect_width, y + rect_height],
                         fill=color)
        
        icons.append(img)
    
    # 保存为 .icns 文件
    if not os.path.exists('app_icon.iconset'):
        os.makedirs('app_icon.iconset')
    
    # 保存不同尺寸的图标
    for i, img in enumerate(icons):
        size = sizes[i]
        img.save(f'app_icon.iconset/icon_{size}x{size}.png')
        if size <= 512:  # 为高分辨率显示器创建 2x 版本
            img.save(f'app_icon.iconset/icon_{size//2}x{size//2}@2x.png')
    
    # 使用 iconutil 创建 .icns 文件
    os.system('iconutil -c icns app_icon.iconset')
    
    # 清理临时文件
    shutil.rmtree('app_icon.iconset')
    
    print("应用图标创建完成！")

if __name__ == '__main__':
    create_app_icon() 