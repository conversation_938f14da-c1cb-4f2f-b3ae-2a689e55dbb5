# 图片合并工具

这是一个用于将多张图片垂直合并的工具，具有图形用户界面。

## 功能特点

- 支持选择目标文件夹
- 可设置每次合并的图片数量（默认3张）
- 自动扫描子文件夹中的图片
- 支持将合并后的图片保存到根目录或原文件夹
- 可选择是否删除原始图片
- 显示合并进度
- 支持暂停/继续/停止操作

## 安装说明

1. 确保已安装 Python 3.8 或更高版本
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序：
   ```bash
   python main.py
   ```
2. 点击"选择文件夹"按钮选择目标文件夹
3. 设置每组要合并的图片数量
4. 选择保存选项（是否保存到根目录、是否删除原始图片）
5. 点击"开始合并"按钮开始处理

## 打包说明

使用 PyInstaller 打包为可执行文件：

```bash
pyinstaller --windowed --name "图片合并工具" main.py
```

## 注意事项

- 支持的图片格式：PNG、JPG、JPEG、BMP、GIF
- 合并后的图片将保存为 PNG 格式
- 建议在合并大量图片前先备份原始文件 