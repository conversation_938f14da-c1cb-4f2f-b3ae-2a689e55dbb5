import sys
import os
import json
import re
import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFileDialog, 
                             QSpinBox, QCheckBox, QProgressBar, QMessageBox,
                             QComboBox, QTextEdit, QSplitter)
from PySide6.QtCore import Qt, QThread, Signal
from PIL import Image
import shutil

CONFIG_FILE = "config.json"

# 支持的图片格式
SUPPORTED_FORMATS = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')

# 版本号
VERSION = "V1.2.0"

class FileExistsStrategy:
    OVERWRITE = "覆盖"
    SKIP = "跳过"
    RENAME = "重命名"
    ASK = "询问"

def natural_sort_key(s):
    """自然排序键函数，用于按数字顺序排序文件名"""
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split(r'(\d+)', s)]

def get_file_size(file_path):
    """获取文件大小，返回人类可读的格式"""
    size = os.path.getsize(file_path)
    # Reuse format_size for consistency after getting the size
    return format_size(size)

def format_size(size_bytes):
    """将字节大小转换为人类可读的格式"""
    if not isinstance(size_bytes, (int, float)):
        return "Invalid size type"
    if size_bytes < 0:
        return "Invalid size (negative)"

    if size_bytes == 0: # Handle 0 case explicitly
        return "0.00 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_idx = 0
    
    # Iterate to find the correct unit
    # Stop at TB or when size is less than 1024
    while size_bytes >= 1024.0 and unit_idx < len(units) - 1:
        size_bytes /= 1024.0
        unit_idx += 1
        
    return f"{size_bytes:.2f} {units[unit_idx]}"

class FolderScanner(QThread):
    scan_completed = Signal(dict)
    log_message = Signal(str)
    
    def __init__(self, source_dir):
        super().__init__()
        self.source_dir = source_dir
        
    def run(self):
        try:
            self.log_message.emit(f"开始扫描文件夹: {self.source_dir}")
            result = {
                'total_folders': 0,
                'total_images': 0,
                'folder_details': []
            }
            
            # 获取所有子文件夹
            subdirs = [d for d in os.listdir(self.source_dir) 
                      if os.path.isdir(os.path.join(self.source_dir, d))]
            
            # 按自然顺序排序子文件夹
            subdirs.sort(key=natural_sort_key)
            
            result['total_folders'] = len(subdirs)
            self.log_message.emit(f"发现 {len(subdirs)} 个子文件夹")
            
            for subdir in subdirs:
                subdir_path = os.path.join(self.source_dir, subdir)
                self.log_message.emit(f"\n扫描文件夹: {subdir}")
                
                image_files = [f for f in os.listdir(subdir_path) 
                             if f.lower().endswith(SUPPORTED_FORMATS) and not f.startswith('.')]
                
                # 按自然顺序排序图片文件
                image_files.sort(key=natural_sort_key)
                
                # 计算文件夹中图片的总大小
                total_size = sum(os.path.getsize(os.path.join(subdir_path, f)) for f in image_files)
                
                folder_info = {
                    'name': subdir,
                    'path': subdir_path,
                    'image_count': len(image_files),
                    'images': image_files,
                    'total_size': total_size
                }
                
                result['folder_details'].append(folder_info)
                result['total_images'] += len(image_files)
                
                self.log_message.emit(f"  发现 {len(image_files)} 张图片")
                self.log_message.emit(f"  总大小: {format_size(total_size)}")
                
            self.log_message.emit(f"\n扫描完成！")
            self.log_message.emit(f"总文件夹数: {result['total_folders']}")
            self.log_message.emit(f"总图片数: {result['total_images']}")
            
            self.scan_completed.emit(result)
            
        except Exception as e:
            error_msg = f"扫描文件夹失败: {str(e)}"
            self.log_message.emit(f"\n错误: {error_msg}")
            self.scan_completed.emit({
                'total_folders': 0,
                'total_images': 0,
                'folder_details': [],
                'error': str(e)
            })

class ImageMergerThread(QThread):
    progress_updated = Signal(int)
    status_updated = Signal(str)
    file_exists = Signal(str, str)  # 发送文件路径和策略
    log_message = Signal(str)
    finished = Signal()
    
    def __init__(self, source_dir, images_per_merge, save_to_root, delete_originals, file_exists_strategy, output_format, naming_format, compression_ratio):
        super().__init__()
        self.source_dir = source_dir
        self.images_per_merge = images_per_merge
        self.save_to_root = save_to_root
        self.delete_originals = delete_originals
        self.file_exists_strategy = file_exists_strategy
        self.output_format = output_format
        self.naming_format = naming_format
        self.compression_ratio = compression_ratio
        self.is_running = True
        self.is_paused = False
        self.current_strategy = file_exists_strategy
        
    def run(self):
        try:
            start_time = datetime.datetime.now()
            self.log_message.emit(f"开始合并图片 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message.emit(f"目标文件夹: {self.source_dir}")
            self.log_message.emit(f"每组图片数: {self.images_per_merge}")
            self.log_message.emit(f"输出格式: {self.output_format}")
            self.log_message.emit(f"命名格式: {self.naming_format}")
            self.log_message.emit(f"保存到根目录: {'是' if self.save_to_root else '否'}")
            self.log_message.emit(f"删除原始图片: {'是' if self.delete_originals else '否'}")
            self.log_message.emit(f"文件存在策略: {self.file_exists_strategy}")
            self.log_message.emit(f"压缩比例: {self.compression_ratio}%")
            
            # 获取所有子文件夹
            subdirs = [d for d in os.listdir(self.source_dir) 
                      if os.path.isdir(os.path.join(self.source_dir, d))]
            
            # 按自然顺序排序子文件夹
            subdirs.sort(key=natural_sort_key)
            
            total_operations = 0
            completed_operations = 0
            total_merged = 0
            total_size_before = 0
            total_size_after = 0
            
            for subdir in subdirs:
                if not self.is_running:
                    break
                    
                subdir_path = os.path.join(self.source_dir, subdir)
                self.log_message.emit(f"\n处理文件夹: {subdir}")
                
                image_files = [f for f in os.listdir(subdir_path) 
                             if f.lower().endswith(SUPPORTED_FORMATS) and not f.startswith('.')]
                
                # 按自然顺序排序图片文件
                image_files.sort(key=natural_sort_key)
                
                # 计算需要合并的组数
                groups = [image_files[i:i + self.images_per_merge] 
                         for i in range(0, len(image_files), self.images_per_merge)]
                total_operations += len(groups)
                
                self.log_message.emit(f"  图片数量: {len(image_files)}")
                self.log_message.emit(f"  将分为 {len(groups)} 组进行合并")
                
                for group_idx, group in enumerate(groups, 1):
                    while self.is_paused:
                        self.msleep(100)
                        if not self.is_running:
                            break
                            
                    if not self.is_running:
                        break
                    
                    self.log_message.emit(f"\n  处理第 {group_idx} 组:")
                    self.log_message.emit(f"    图片: {', '.join(group)}")
                    
                    # 计算原始图片总大小
                    group_size_before = sum(os.path.getsize(os.path.join(subdir_path, f)) for f in group)
                    total_size_before += group_size_before
                    
                    # 打开所有图片
                    images = []
                    for img_file in group:
                        img_path = os.path.join(subdir_path, img_file)
                        try:
                            img = Image.open(img_path)
                            
                            # 处理不同格式的图片
                            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                                # 对于带透明通道的图片，转换为RGB
                                img = img.convert('RGB')
                            elif img.mode == 'CMYK':
                                # 对于CMYK模式的图片（某些JPG格式），转换为RGB
                                img = img.convert('RGB')
                            
                            # 应用压缩
                            if self.compression_ratio < 100:
                                original_width, original_height = img.size
                                new_width = int(original_width * self.compression_ratio / 100)
                                new_height = int(original_height * self.compression_ratio / 100)
                                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                            
                            images.append(img)
                            self.log_message.emit(f"    已加载: {img_file} ({img.width}x{img.height})")
                        except Exception as e:
                            self.log_message.emit(f"    错误: 无法打开图片 {img_file}: {str(e)}")
                            continue
                    
                    if not images:
                        self.log_message.emit("    跳过: 没有可用的图片")
                        continue
                    
                    # 计算合并后图片的尺寸
                    max_width = max(img.width for img in images)
                    total_height = sum(img.height for img in images)
                    
                    # 创建新图片
                    merged_image = Image.new('RGB', (max_width, total_height))
                    
                    # 粘贴图片
                    y_offset = 0
                    for img in images:
                        merged_image.paste(img, (0, y_offset))
                        y_offset += img.height
                        img.close()
                    
                    # 使用新的命名格式生成文件名
                    try:
                        new_filename = self.naming_format.format(
                            folder=subdir,
                            index=group_idx
                        ) + f".{self.output_format}"
                    except Exception as e:
                        # 使用默认命名格式，不显示错误信息
                        new_filename = f"{subdir}-{group_idx:03d}.{self.output_format}"
                    
                    save_dir = self.source_dir if self.save_to_root else subdir_path
                    
                    # 确保目标目录存在
                    if not os.path.exists(save_dir):
                        try:
                            os.makedirs(save_dir)
                        except Exception:
                            # 目录创建失败，跳过当前文件
                            continue
                    
                    save_path = os.path.join(save_dir, new_filename)
                    
                    # 处理文件已存在的情况
                    if os.path.exists(save_path):
                        if self.current_strategy == FileExistsStrategy.ASK:
                            self.file_exists.emit(save_path, new_filename)
                            while self.current_strategy == FileExistsStrategy.ASK:
                                self.msleep(100)
                                if not self.is_running:
                                    break
                        
                        if self.current_strategy == FileExistsStrategy.SKIP:
                            self.log_message.emit(f"    跳过: 文件已存在 {new_filename}")
                            continue
                        elif self.current_strategy == FileExistsStrategy.RENAME:
                            base, ext = os.path.splitext(save_path)
                            counter = 1
                            while os.path.exists(f"{base}_{counter}{ext}"):
                                counter += 1
                            save_path = f"{base}_{counter}{ext}"
                            new_filename = os.path.basename(save_path)
                            self.log_message.emit(f"    重命名为: {new_filename}")
                    
                    try:
                        # 设置保存质量
                        save_kwargs = {'format': self.output_format.upper()}
                        if self.output_format.lower() in ['jpg', 'jpeg']:
                            save_kwargs['quality'] = 95  # JPEG质量
                            save_kwargs['optimize'] = True  # 优化JPEG
                        elif self.output_format.lower() == 'webp':
                            save_kwargs['quality'] = 95  # WebP质量
                        
                        merged_image.save(save_path, **save_kwargs)
                        merged_size = os.path.getsize(save_path)
                        total_size_after += merged_size
                        total_merged += 1
                        
                        self.log_message.emit(f"    已保存: {new_filename}")
                        self.log_message.emit(f"    尺寸: {max_width}x{total_height}")
                        self.log_message.emit(f"    大小: {get_file_size(save_path)}")
                        
                        # 删除原始图片
                        if self.delete_originals:
                            for img_file in group:
                                try:
                                    os.remove(os.path.join(subdir_path, img_file))
                                    self.log_message.emit(f"    已删除: {img_file}")
                                except Exception:
                                    # 删除失败，继续处理
                                    continue
                    except Exception as e:
                        # 只显示重要的错误信息
                        if "Permission denied" in str(e):
                            self.log_message.emit(f"    错误: 没有权限保存文件 {new_filename}")
                        elif "No space left" in str(e):
                            self.log_message.emit(f"    错误: 磁盘空间不足")
                        continue
                    
                    completed_operations += 1
                    progress = int((completed_operations / total_operations) * 100)
                    self.progress_updated.emit(progress)
                    self.status_updated.emit(f"正在处理: {subdir} - {group_idx}/{len(groups)}")
            
            end_time = datetime.datetime.now()
            duration = end_time - start_time
            
            self.log_message.emit(f"\n合并完成！")
            self.log_message.emit(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message.emit(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message.emit(f"总耗时: {duration}")
            self.log_message.emit(f"总合并数: {total_merged}")
            self.log_message.emit(f"原始总大小: {format_size(total_size_before)}")
            self.log_message.emit(f"合并后总大小: {format_size(total_size_after)}")
            
            self.status_updated.emit("处理完成！")
            self.finished.emit()
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log_message.emit(f"\n错误: 处理过程中出现异常。\n详细信息: {error_details}")
            # 只显示重要的错误信息给用户，但记录完整错误
            if "Permission denied" in str(e):
                self.log_message.emit(f"用户提示: 没有权限访问文件夹")
            elif "No space left" in str(e):
                self.log_message.emit(f"用户提示: 磁盘空间不足")
            else:
                self.log_message.emit(f"用户提示: 处理过程中出现未知异常")
            self.status_updated.emit("处理失败")
            self.finished.emit()
    
    def set_file_exists_strategy(self, strategy):
        self.current_strategy = strategy
    
    def pause(self):
        self.is_paused = True
        
    def resume(self):
        self.is_paused = False
        
    def stop(self):
        self.is_running = False

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"图片合并工具 {VERSION}")
        self.setMinimumSize(1000, 800)
        
        # 初始化配置变量
        self.source_dir = None
        self.images_per_merge = 3
        self.save_to_root_checked = True
        self.delete_originals_checked = False
        self.file_exists_strategy_checked = FileExistsStrategy.OVERWRITE
        self.output_format = "webp"  # 新增：输出格式
        self.naming_format = "{folder}-{index:03d}"  # 新增：命名格式
        self.compression_ratio = 100  # 新增：压缩比例，默认100%
        
        # 加载配置
        self.load_config()
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 上部分：控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        
        # 选择文件夹按钮
        folder_layout = QHBoxLayout()
        self.folder_label = QLabel(self.source_dir if self.source_dir else "未选择文件夹")
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.clicked.connect(self.select_folder)
        folder_layout.addWidget(self.folder_label)
        folder_layout.addWidget(self.select_folder_btn)
        control_layout.addLayout(folder_layout)
        
        # 设置每组合并图片数量
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("每组图片数量:"))
        self.images_count = QSpinBox()
        self.images_count.setRange(2, 10)
        self.images_count.setValue(self.images_per_merge)
        self.images_count.valueChanged.connect(self.update_merge_info)
        count_layout.addWidget(self.images_count)
        count_layout.addStretch()
        control_layout.addLayout(count_layout)
        
        # 新增：压缩比例设置
        compression_layout = QHBoxLayout()
        compression_layout.addWidget(QLabel("压缩比例:"))
        self.compression_ratio_spin = QSpinBox()
        self.compression_ratio_spin.setRange(1, 100)
        self.compression_ratio_spin.setValue(self.compression_ratio)
        self.compression_ratio_spin.setSuffix("%")
        compression_layout.addWidget(self.compression_ratio_spin)
        compression_layout.addStretch()
        control_layout.addLayout(compression_layout)
        
        # 新增：输出格式选择
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("输出格式:"))
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(['webp', 'png', 'jpg', 'jpeg'])
        self.output_format_combo.setCurrentText(self.output_format)
        format_layout.addWidget(self.output_format_combo)
        format_layout.addStretch()
        control_layout.addLayout(format_layout)
        
        # 新增：命名格式设置
        naming_layout = QHBoxLayout()
        naming_layout.addWidget(QLabel("命名格式:"))
        self.naming_format_edit = QTextEdit()
        self.naming_format_edit.setMaximumHeight(50)
        self.naming_format_edit.setPlaceholderText("可用变量: {folder} - 文件夹名, {index} - 序号")
        self.naming_format_edit.setText(self.naming_format)
        naming_layout.addWidget(self.naming_format_edit)
        control_layout.addLayout(naming_layout)
        
        # 文件存在时的处理策略
        strategy_layout = QHBoxLayout()
        strategy_layout.addWidget(QLabel("文件已存在时:"))
        self.file_exists_strategy = QComboBox()
        self.file_exists_strategy.addItems([
            FileExistsStrategy.OVERWRITE,
            FileExistsStrategy.SKIP,
            FileExistsStrategy.RENAME,
            FileExistsStrategy.ASK
        ])
        self.file_exists_strategy.setCurrentText(self.file_exists_strategy_checked)
        strategy_layout.addWidget(self.file_exists_strategy)
        strategy_layout.addStretch()
        control_layout.addLayout(strategy_layout)
        
        # 保存选项
        options_layout = QHBoxLayout()
        self.save_to_root = QCheckBox("保存到根目录")
        self.save_to_root.setChecked(self.save_to_root_checked)
        self.delete_originals = QCheckBox("删除原始图片")
        self.delete_originals.setChecked(self.delete_originals_checked)
        options_layout.addWidget(self.save_to_root)
        options_layout.addWidget(self.delete_originals)
        control_layout.addLayout(options_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        control_layout.addWidget(self.status_label)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始合并")
        self.pause_btn = QPushButton("暂停")
        self.stop_btn = QPushButton("停止")
        
        self.start_btn.clicked.connect(self.start_merging)
        self.pause_btn.clicked.connect(self.toggle_pause)
        self.stop_btn.clicked.connect(self.stop_merging)
        
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.pause_btn)
        buttons_layout.addWidget(self.stop_btn)
        control_layout.addLayout(buttons_layout)
        
        # 下部分：日志显示
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        
        # 添加到分割器
        splitter.addWidget(control_panel)
        splitter.addWidget(self.log_display)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        self.merger_thread = None
        self.scanner = None
        self.scan_data = None
        
    def log(self, message):
        """添加日志消息"""
        self.log_display.append(message)
        # 滚动到底部
        self.log_display.verticalScrollBar().setValue(
            self.log_display.verticalScrollBar().maximum()
        )
        
    def select_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择目标文件夹", self.source_dir)
        if folder:
            self.source_dir = folder
            self.folder_label.setText(folder)
            self.save_config()
            self.scan_folder()
            
    def scan_folder(self):
        """扫描文件夹"""
        if not self.source_dir:
            return
            
        self.log_display.clear()
        self.log("正在扫描文件夹...")
        self.status_label.setText("正在扫描...")
        
        self.scanner = FolderScanner(self.source_dir)
        self.scanner.scan_completed.connect(self.handle_scan_result)
        self.scanner.log_message.connect(self.log)
        self.scanner.start()
        
    def handle_scan_result(self, result):
        """处理扫描结果"""
        if 'error' in result:
            self.log(f"扫描出错: {result['error']}")
            return
            
        self.scan_data = result
        self.update_merge_info()
        self.status_label.setText("扫描完成")
        
    def update_merge_info(self):
        """更新合并信息"""
        if not self.scan_data:
            return
            
        total_images = self.scan_data['total_images']
        images_per_merge = self.images_count.value()
        total_groups = (total_images + images_per_merge - 1) // images_per_merge
        
        self.log(f"\n合并信息:")
        self.log(f"每组图片数: {images_per_merge}")
        self.log(f"预计生成图片数: {total_groups}")
        
    def start_merging(self):
        if not self.source_dir:
            QMessageBox.warning(self, "警告", "请先选择目标文件夹！")
            return
            
        self.merger_thread = ImageMergerThread(
            self.source_dir,
            self.images_count.value(),
            self.save_to_root.isChecked(),
            self.delete_originals.isChecked(),
            self.file_exists_strategy.currentText(),
            self.output_format_combo.currentText(),
            self.naming_format_edit.toPlainText(),
            self.compression_ratio_spin.value()
        )
        
        self.merger_thread.progress_updated.connect(self.update_progress)
        self.merger_thread.status_updated.connect(self.update_status)
        self.merger_thread.file_exists.connect(self.handle_file_exists)
        self.merger_thread.log_message.connect(self.log)
        self.merger_thread.finished.connect(self.merging_finished)
        
        self.merger_thread.start()
        
        self.start_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        
        # 保存配置
        self.save_config()
        
    def handle_file_exists(self, file_path, file_name):
        """处理文件已存在的情况"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Question)
        msg.setWindowTitle("文件已存在")
        msg.setText(f"文件 {file_name} 已存在，如何处理？")
        
        overwrite_btn = msg.addButton("覆盖", QMessageBox.ActionRole)
        skip_btn = msg.addButton("跳过", QMessageBox.ActionRole)
        rename_btn = msg.addButton("重命名", QMessageBox.ActionRole)
        all_overwrite_btn = msg.addButton("全部覆盖", QMessageBox.ActionRole)
        all_skip_btn = msg.addButton("全部跳过", QMessageBox.ActionRole)
        all_rename_btn = msg.addButton("全部重命名", QMessageBox.ActionRole)
        
        msg.exec()
        
        clicked_button = msg.clickedButton()
        
        if clicked_button == overwrite_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.OVERWRITE)
        elif clicked_button == skip_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.SKIP)
        elif clicked_button == rename_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.RENAME)
        elif clicked_button == all_overwrite_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.OVERWRITE)
            self.file_exists_strategy.setCurrentText(FileExistsStrategy.OVERWRITE)
            self.save_config()
        elif clicked_button == all_skip_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.SKIP)
            self.file_exists_strategy.setCurrentText(FileExistsStrategy.SKIP)
            self.save_config()
        elif clicked_button == all_rename_btn:
            self.merger_thread.set_file_exists_strategy(FileExistsStrategy.RENAME)
            self.file_exists_strategy.setCurrentText(FileExistsStrategy.RENAME)
            self.save_config()
        
    def toggle_pause(self):
        if self.merger_thread:
            if self.merger_thread.is_paused:
                self.merger_thread.resume()
                self.pause_btn.setText("暂停")
                self.log("继续处理...")
            else:
                self.merger_thread.pause()
                self.pause_btn.setText("继续")
                self.log("已暂停")
                
    def stop_merging(self):
        if self.merger_thread:
            self.merger_thread.stop()
            self.log("已停止处理")
            
    def update_progress(self, value):
        self.progress_bar.setValue(value)
        
    def update_status(self, message):
        self.status_label.setText(message)
        
    def merging_finished(self):
        self.start_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        self.pause_btn.setText("暂停")
        self.progress_bar.setValue(0)
        
    def closeEvent(self, event):
        """窗口关闭时保存配置"""
        self.save_config()
        event.accept()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.source_dir = config.get('source_dir')
                    self.images_per_merge = config.get('images_per_merge', 3)
                    self.save_to_root_checked = config.get('save_to_root', True)
                    self.delete_originals_checked = config.get('delete_originals', False)
                    self.file_exists_strategy_checked = config.get('file_exists_strategy', FileExistsStrategy.OVERWRITE)
                    self.output_format = config.get('output_format', "webp")
                    self.naming_format = config.get('naming_format', "{folder}-{index:03d}")
                    self.compression_ratio = config.get('compression_ratio', 100)
        except Exception as e:
            self.log(f"加载配置文件失败: {str(e)}")
            
    def save_config(self):
        """保存配置文件"""
        config = {
            'source_dir': self.source_dir,
            'images_per_merge': self.images_count.value(),
            'save_to_root': self.save_to_root.isChecked(),
            'delete_originals': self.delete_originals.isChecked(),
            'file_exists_strategy': self.file_exists_strategy.currentText(),
            'output_format': self.output_format_combo.currentText(),
            'naming_format': self.naming_format_edit.toPlainText(),
            'compression_ratio': self.compression_ratio
        }
        
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            self.log(f"保存配置文件失败: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())